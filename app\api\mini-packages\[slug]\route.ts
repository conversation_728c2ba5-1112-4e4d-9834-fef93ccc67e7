import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch single mini package by slug (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Fetch mini package with all related data (only if published)
    const { data: miniPackageData, error: miniPackageError } = await supabase
      .from('sas_mini_packages')
      .select(`
        *,
        sas_mini_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_mini_package_itinerary (
          id,
          day_number,
          title,
          description,
          activities,
          accommodation,
          meals,
          sort_order
        ),
        sas_mini_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', slug)
      .eq('status', 'published') // Only return published mini packages
      .single();

    if (miniPackageError) {
      if (miniPackageError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Mini package not found or not available' },
          { status: 404 }
        );
      }
      console.error('Supabase error:', miniPackageError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch mini package' },
        { status: 500 }
      );
    }

    // Debug: Log the raw mini package data from database
    console.log('Single Mini Package API - Raw data from DB:', {
      id: miniPackageData.id,
      title: miniPackageData.title,
      slug: miniPackageData.slug,
      image_url: miniPackageData.image_url,
      hero_image_url: miniPackageData.hero_image_url,
      image_alt: miniPackageData.image_alt,
      hero_image_alt: miniPackageData.hero_image_alt
    });

    // Calculate lowest price for display
    const prices = [
      miniPackageData.pricing_solo,
      miniPackageData.pricing_honeymoon,
      miniPackageData.pricing_family,
      miniPackageData.pricing_group
    ];
    const lowestPrice = Math.min(...prices);

    // Transform data to match frontend expectations
    const transformedMiniPackage = {
      id: miniPackageData.id,
      title: miniPackageData.title,
      slug: miniPackageData.slug,
      description: miniPackageData.description,
      overview: miniPackageData.overview,
      difficulty: miniPackageData.difficulty,
      category: miniPackageData.category,
      location: miniPackageData.location,
      duration: miniPackageData.duration,
      
      // Pricing information
      pricing: {
        solo: { price: miniPackageData.pricing_solo, text: 'Per Person' },
        honeymoon: { price: miniPackageData.pricing_honeymoon, text: 'Per Person' },
        family: { price: miniPackageData.pricing_family, text: 'Per Person' },
        group: { price: miniPackageData.pricing_group, text: 'Per Person' }
      },
      lowestPrice: lowestPrice,
      
      // Images
      heroImage: miniPackageData.hero_image_url || miniPackageData.image_url,
      heroImageUrl: miniPackageData.hero_image_url || miniPackageData.image_url,
      heroImageAlt: miniPackageData.hero_image_alt || miniPackageData.image_alt,
      imageUrl: miniPackageData.image_url,
      imageAlt: miniPackageData.image_alt,
      image: miniPackageData.image_url, // For card compatibility
      image_url: miniPackageData.image_url, // For card compatibility
      
      // Content arrays
      highlights: miniPackageData.highlights || [],
      whatToPack: miniPackageData.packing_list || [],
      packingList: miniPackageData.packing_list || [], // For backward compatibility
      includes: miniPackageData.includes || [],
      excludes: miniPackageData.excludes || [],
      
      // SEO data (for metadata generation)
      seoTitle: miniPackageData.seo_title,
      seoDescription: miniPackageData.seo_description,
      seoKeywords: miniPackageData.seo_keywords || [],
      ogImageUrl: miniPackageData.og_image_url,
      schemaData: miniPackageData.schema_data,
      
      // Related content
      contentBlocks: miniPackageData.sas_mini_package_content_blocks?.sort((a, b) => a.sort_order - b.sort_order) || [],
      itinerary: miniPackageData.sas_mini_package_itinerary?.sort((a, b) => a.sort_order - b.sort_order) || [],
      gallery: miniPackageData.sas_mini_package_images?.sort((a, b) => a.sort_order - b.sort_order) || [],
      
      // Metadata
      status: miniPackageData.status,
      createdAt: miniPackageData.created_at,
      updatedAt: miniPackageData.updated_at,
      publishedAt: miniPackageData.published_at
    };

    return NextResponse.json({
      success: true,
      data: transformedMiniPackage
    });

  } catch (error) {
    console.error('Error fetching mini package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch mini package' },
      { status: 500 }
    );
  }
}
