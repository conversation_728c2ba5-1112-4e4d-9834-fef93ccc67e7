/**
 * Configuration settings for different environments
 */

export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';

// Database configuration
export const dbConfig = {
  // Timeout settings for database operations
  defaultTimeout: isDevelopment ? 10000 : 30000, // 10s dev, 30s prod
  staticGenerationTimeout: isDevelopment ? 5000 : 15000, // 5s dev, 15s prod
  
  // Static generation settings
  enableStaticGeneration: !isDevelopment, // Disable in development
  
  // Retry settings
  maxRetries: isDevelopment ? 1 : 3,
  retryDelay: 1000,
};

// Supabase configuration
export const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
};

// TinyMCE configuration
export const tinyMCEConfig = {
  apiKey: process.env.TINYMCE_API_KEY,
};

// Development helpers
export const devHelpers = {
  logFetches: isDevelopment,
  skipStaticGeneration: isDevelopment,
  enableDetailedErrors: isDevelopment,
};

// Error messages
export const errorMessages = {
  networkTimeout: 'Network timeout - this usually happens in local development. The page will work in production.',
  databaseError: 'Database connection error. Please try again later.',
  notFound: 'The requested content could not be found.',
};
