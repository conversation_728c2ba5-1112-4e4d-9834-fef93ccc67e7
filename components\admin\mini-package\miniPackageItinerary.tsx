"use client";

import React, { useState, useEffect } from "react";
import { Plus, Minus, X } from "lucide-react";

interface ItineraryItem {
  id: number;
  day: number;
  title: string;
  description: string;
  activities?: string[];
  accommodation?: string;
  meals?: string[];
  isExpanded: boolean;
  isEditing: boolean;
}

interface MiniPackageItineraryProps {
  initialItinerary?: ItineraryItem[];
  onSave?: (itinerary: ItineraryItem[]) => void;
}

const MiniPackageItinerary: React.FC<MiniPackageItineraryProps> = ({
  initialItinerary = [],
  onSave
}) => {
  const [itineraries, setItineraries] = useState<ItineraryItem[]>(
    initialItinerary.length > 0 ? initialItinerary : [
      {
        id: 1,
        day: 1,
        title: "Arrival at Kasese Airport and Check in",
        description:
          "Your safari begins with a warm welcome at Kasese Airport, followed by a wonderful drive at your stay near Queen Elizabeth National Park...",
        activities: ["Airport pickup", "Transfer to lodge", "Welcome briefing"],
        accommodation: "Lodge near Queen Elizabeth National Park",
        meals: ["Dinner"],
        isExpanded: true,
        isEditing: false,
      },
    ]
  );

  // Update itineraries when initialItinerary changes (for editing existing content)
  useEffect(() => {
    if (initialItinerary && initialItinerary.length > 0) {
      setItineraries(initialItinerary);
    }
  }, [initialItinerary]);

  // Debounced save effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSave) {
        onSave(itineraries);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [itineraries, onSave]);

  const addNewDay = () => {
    const newDay: ItineraryItem = {
      id: Date.now(),
      day: itineraries.length + 1,
      title: "",
      description: "",
      activities: [],
      accommodation: "",
      meals: [],
      isExpanded: true,
      isEditing: true,
    };
    const newItineraries = [...itineraries, newDay];
    setItineraries(newItineraries);
  };

  const removeDay = (id: number) => {
    if (itineraries.length > 1) {
      const filtered = itineraries.filter((item) => item.id !== id);
      const reNumbered = filtered.map((item, index) => ({
        ...item,
        day: index + 1,
      }));
      setItineraries(reNumbered);
    }
  };

  const toggleExpanded = (id: number) => {
    setItineraries(
      itineraries.map((item) =>
        item.id === id ? { ...item, isExpanded: !item.isExpanded } : item
      )
    );
  };

  const toggleEditing = (id: number) => {
    setItineraries(
      itineraries.map((item) =>
        item.id === id ? { ...item, isEditing: !item.isEditing } : item
      )
    );
  };

  const updateItinerary = (
    id: number,
    field: keyof ItineraryItem,
    value: string | string[]
  ) => {
    const newItineraries = itineraries.map((item) =>
      item.id === id ? { ...item, [field]: value } : item
    );
    setItineraries(newItineraries);
  };

  const updateActivities = (id: number, activities: string[]) => {
    updateItinerary(id, "activities", activities);
  };

  const updateMeals = (id: number, meals: string[]) => {
    updateItinerary(id, "meals", meals);
  };

  return (
    <div className="min-h-auto bg-[var(--background)] p-6">
      <div className="max-w-full mx-auto">
        {/* Itinerary Items */}
        <div className="space-y-4">
          {itineraries.map((item) => (
            <div
              key={item.id}
              className="bg-[var(--white)] rounded-lg shadow-sm border border-gray-200"
            >
              {/* Day Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="bg-[var(--btn)] text-[var(--white)] rounded-full w-8 h-8 flex items-center justify-center font-semibold mr-3">
                    {item.day}
                  </div>
                  <h3 className="text-lg font-semibold text-[var(--text)]">
                    Day {item.day}
                  </h3>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => toggleEditing(item.id)}
                    className="px-3 py-1 text-sm bg-[var(--primary-background)] text-[var(--btn)] rounded-md hover:bg-[var(--secondary-background)] transition-colors"
                  >
                    {item.isEditing ? "Save" : "Edit"}
                  </button>

                  <button
                    onClick={() => toggleExpanded(item.id)}
                    className="p-1 text-[var(--text)] hover:text-[var(--btn)]"
                  >
                    {item.isExpanded ? <Minus size={16} /> : <Plus size={16} />}
                  </button>

                  {itineraries.length > 1 && (
                    <button
                      onClick={() => removeDay(item.id)}
                      className="p-1 text-[var(--accent)] hover:text-red-700"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
              </div>

              {/* Day Content */}
              {item.isExpanded && (
                <div className="p-4">
                  {item.isEditing ? (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-[var(--text)] mb-2">
                          Day Title
                        </label>
                        <input
                          type="text"
                          value={item.title}
                          onChange={(e) =>
                            updateItinerary(item.id, "title", e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                          placeholder="Enter day title..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-[var(--text)] mb-2">
                          Description
                        </label>
                        <textarea
                          value={item.description}
                          onChange={(e) =>
                            updateItinerary(
                              item.id,
                              "description",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)] resize-vertical"
                          rows={4}
                          placeholder="Describe the activities and experiences for this day..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-[var(--text)] mb-2">
                          Activities (one per line)
                        </label>
                        <textarea
                          value={item.activities?.join('\n') || ''}
                          onChange={(e) =>
                            updateActivities(item.id, e.target.value.split('\n').filter(line => line.trim()))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)] resize-vertical"
                          rows={3}
                          placeholder="Airport pickup&#10;Transfer to lodge&#10;Welcome briefing"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-[var(--text)] mb-2">
                          Accommodation
                        </label>
                        <input
                          type="text"
                          value={item.accommodation || ''}
                          onChange={(e) =>
                            updateItinerary(item.id, "accommodation", e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                          placeholder="Lodge near Queen Elizabeth National Park"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-[var(--text)] mb-2">
                          Meals (one per line)
                        </label>
                        <textarea
                          value={item.meals?.join('\n') || ''}
                          onChange={(e) =>
                            updateMeals(item.id, e.target.value.split('\n').filter(line => line.trim()))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)] resize-vertical"
                          rows={2}
                          placeholder="Breakfast&#10;Lunch&#10;Dinner"
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-lg font-semibold text-[var(--text)] mb-2">
                          {item.title || "Untitled Day"}
                        </h4>
                        <p className="text-[var(--text)] opacity-70 leading-relaxed">
                          {item.description || "No description provided."}
                        </p>
                      </div>

                      {/* Activities */}
                      {item.activities && item.activities.length > 0 && (
                        <div>
                          <h5 className="text-sm font-semibold text-[var(--text)] mb-2">Activities:</h5>
                          <ul className="list-disc list-inside space-y-1">
                            {item.activities.map((activity, index) => (
                              <li key={index} className="text-[var(--text)] opacity-70 text-sm">
                                {activity}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Accommodation */}
                      {item.accommodation && (
                        <div>
                          <h5 className="text-sm font-semibold text-[var(--text)] mb-1">Accommodation:</h5>
                          <p className="text-[var(--text)] opacity-70 text-sm">{item.accommodation}</p>
                        </div>
                      )}

                      {/* Meals */}
                      {item.meals && item.meals.length > 0 && (
                        <div>
                          <h5 className="text-sm font-semibold text-[var(--text)] mb-2">Meals:</h5>
                          <ul className="list-disc list-inside space-y-1">
                            {item.meals.map((meal, index) => (
                              <li key={index} className="text-[var(--text)] opacity-70 text-sm">
                                {meal}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add New Day Button */}
        <div className="mt-6 text-center">
          <button
            onClick={addNewDay}
            className="flex items-center mx-auto px-6 py-3 bg-[var(--btn)] text-[var(--white)] rounded-md hover:bg-[var(--light-green)] transition-colors"
          >
            <Plus className="mr-2" size={16} />
            Add New Day
          </button>
        </div>
      </div>
    </div>
  );
};

export default MiniPackageItinerary;
