'use client'

import React from 'react'
import Image from 'next/image'
import { ContentBlock } from '@/lib/blog-service'

interface ContentRendererProps {
  contentBlocks: ContentBlock[]
}

const ContentRenderer: React.FC<ContentRendererProps> = ({ contentBlocks }) => {
  const renderContentBlock = (block: ContentBlock) => {
    const { block_type, content, id } = block

    switch (block_type) {
      case 'h2':
        return (
          <h2 
            key={id} 
            className="text-3xl font-bold text-gray-900 mb-6 mt-8 leading-tight"
          >
            {typeof content === 'string' ? content : content.content || content}
          </h2>
        )

      case 'h3':
        return (
          <h3 
            key={id} 
            className="text-2xl font-semibold text-gray-900 mb-4 mt-6 leading-tight"
          >
            {typeof content === 'string' ? content : content.content || content}
          </h3>
        )

      case 'h4':
        return (
          <h4 
            key={id} 
            className="text-xl font-semibold text-gray-900 mb-3 mt-5 leading-tight"
          >
            {typeof content === 'string' ? content : content.content || content}
          </h4>
        )

      case 'h5':
        return (
          <h5 
            key={id} 
            className="text-lg font-semibold text-gray-900 mb-3 mt-4 leading-tight"
          >
            {typeof content === 'string' ? content : content.content || content}
          </h5>
        )

      case 'h6':
        return (
          <h6 
            key={id} 
            className="text-base font-semibold text-gray-900 mb-2 mt-4 leading-tight"
          >
            {typeof content === 'string' ? content : content.content || content}
          </h6>
        )

      case 'paragraph':
        const paragraphContent = typeof content === 'string' ? content : content.content || content
        return (
          <div 
            key={id} 
            className="text-lg leading-relaxed text-gray-700 mb-6 prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: paragraphContent }}
          />
        )

      case 'image':
        const imageData = typeof content === 'object' ? content : {}
        const imageWidth = imageData.width || 'full'

        // Determine container width based on image width setting
        const getImageContainerClass = (width: string) => {
          switch (width) {
            case 'sm': return 'max-w-md mx-auto'
            case 'md': return 'max-w-2xl mx-auto'
            case 'lg': return 'max-w-4xl mx-auto'
            case 'full':
            default: return 'w-full'
          }
        }

        return (
          <figure key={id} className="my-8">
            <div className={`relative ${getImageContainerClass(imageWidth)} rounded-lg overflow-hidden`}>
              <div className="relative w-full" style={{ aspectRatio: '16/10' }}>
                <Image
                  src={imageData.src || ''}
                  alt={imageData.alt || 'Blog image'}
                  fill
                  className="object-contain bg-gray-50"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                />
              </div>
            </div>
            {imageData.caption && (
              <figcaption className="text-center text-sm text-gray-600 mt-3 italic">
                {imageData.caption}
              </figcaption>
            )}
          </figure>
        )

      case 'video':
        const videoData = typeof content === 'object' ? content : {}
        const videoSrc = videoData.src || ''
        const videoWidth = videoData.width || 'lg'

        // Determine container width based on video width setting
        const getVideoContainerClass = (width: string) => {
          switch (width) {
            case 'sm': return 'max-w-md mx-auto'
            case 'md': return 'max-w-2xl mx-auto'
            case 'lg': return 'max-w-4xl mx-auto'
            case 'full':
            default: return 'w-full'
          }
        }

        // Check if it's a YouTube video
        const isYouTubeVideo = videoSrc.includes('youtube.com') || videoSrc.includes('youtu.be')

        // Extract YouTube video ID and create embed URL
        const getYouTubeEmbedUrl = (url: string) => {
          let videoId = ''

          if (url.includes('youtube.com/watch?v=')) {
            videoId = url.split('v=')[1]?.split('&')[0]
          } else if (url.includes('youtube.com/embed/')) {
            videoId = url.split('/embed/')[1]?.split('?')[0]
          } else if (url.includes('youtu.be/')) {
            videoId = url.split('youtu.be/')[1]?.split('?')[0]
          }

          return videoId ? `https://www.youtube.com/embed/${videoId}` : url
        }

        return (
          <figure key={id} className="my-8">
            <div className={`relative ${getVideoContainerClass(videoWidth)} rounded-lg overflow-hidden bg-gray-100`}>
              <div className="relative w-full" style={{ aspectRatio: '16/9' }}>
                {isYouTubeVideo ? (
                  <iframe
                    src={getYouTubeEmbedUrl(videoSrc)}
                    title={videoData.caption || 'Video'}
                    className="absolute inset-0 w-full h-full"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                ) : (
                  <video
                    src={videoSrc}
                    poster={videoData.poster}
                    controls
                    className="absolute inset-0 w-full h-full object-cover"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
              </div>
            </div>
            {videoData.caption && (
              <figcaption className="text-center text-sm text-gray-600 mt-3 italic">
                {videoData.caption}
              </figcaption>
            )}
          </figure>
        )

      case 'listing':
        const listData = Array.isArray(content) ? content : (content.items || [])
        const listType = typeof content === 'object' && !Array.isArray(content) ? content.listType : 'unordered'
        
        if (listType === 'ordered') {
          return (
            <ol key={id} className="list-decimal list-inside space-y-2 mb-6 text-lg text-gray-700 ml-4">
              {listData.map((item: string, index: number) => (
                <li key={index} className="leading-relaxed">{item}</li>
              ))}
            </ol>
          )
        } else {
          return (
            <ul key={id} className="list-disc list-inside space-y-2 mb-6 text-lg text-gray-700 ml-4">
              {listData.map((item: string, index: number) => (
                <li key={index} className="leading-relaxed">{item}</li>
              ))}
            </ul>
          )
        }

      case 'quote':
        const quoteContent = typeof content === 'string' ? content : content.content || content
        const quoteAuthor = typeof content === 'object' ? content.author : undefined
        const quoteSource = typeof content === 'object' ? content.source : undefined
        
        return (
          <blockquote key={id} className="border-l-4 border-blue-500 pl-6 py-4 my-8 bg-gray-50 rounded-r-lg">
            <div 
              className="text-xl italic text-gray-800 leading-relaxed mb-3"
              dangerouslySetInnerHTML={{ __html: quoteContent }}
            />
            {(quoteAuthor || quoteSource) && (
              <cite className="text-sm text-gray-600 not-italic">
                {quoteAuthor && <span className="font-semibold">— {quoteAuthor}</span>}
                {quoteSource && <span className="ml-2">({quoteSource})</span>}
              </cite>
            )}
          </blockquote>
        )

      case 'divider':
        return (
          <hr key={id} className="my-12 border-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
        )

      default:
        return (
          <div key={id} className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <p className="text-red-600 text-sm">
              Unknown content block type: {block_type}
            </p>
          </div>
        )
    }
  }

  if (!contentBlocks || contentBlocks.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No content available for this blog post.</p>
      </div>
    )
  }

  return (
    <div className="prose prose-lg max-w-none">
      {contentBlocks
        .sort((a, b) => a.sort_order - b.sort_order)
        .map(renderContentBlock)}
    </div>
  )
}

export default ContentRenderer
