import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { sendMiniPackageBookingConfirmationEmail, sendMiniPackageBookingNotificationEmail } from '@/lib/email-service';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface MiniPackageBookingData {
  mini_package_id?: string;
  mini_package_title: string;
  package_type: 'solo' | 'honeymoon' | 'family' | 'group';
  package_price: number;
  full_name: string;
  email: string;
  phone: string;
  number_of_travelers: number;
  preferred_travel_date: string;
  special_requests?: string;
}

// GET - Fetch mini package bookings (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('sas_mini_package_bookings')
      .select('*', { count: 'exact' });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('booking_status', status);
    }

    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,mini_package_title.ilike.%${search}%,booking_reference.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: bookings, error, count } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch mini package bookings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: bookings || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching mini package bookings:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new mini package booking
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['miniPackageTitle', 'packageType', 'packagePrice', 'fullName', 'email', 'phone', 'numberOfTravelers', 'preferredTravelDate'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate package type
    const validPackageTypes = ['solo', 'honeymoon', 'family', 'group'];
    if (!validPackageTypes.includes(body.packageType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid package type' },
        { status: 400 }
      );
    }

    // Validate number of travelers
    const numberOfTravelers = parseInt(body.numberOfTravelers);
    if (isNaN(numberOfTravelers) || numberOfTravelers < 1) {
      return NextResponse.json(
        { success: false, error: 'Invalid number of travelers' },
        { status: 400 }
      );
    }

    // Validate package price
    const packagePrice = parseFloat(body.packagePrice);
    if (isNaN(packagePrice) || packagePrice < 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid package price' },
        { status: 400 }
      );
    }

    // Validate travel date (must be in the future)
    const travelDate = new Date(body.preferredTravelDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (travelDate < today) {
      return NextResponse.json(
        { success: false, error: 'Travel date must be in the future' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const bookingData: MiniPackageBookingData = {
      mini_package_id: body.miniPackageId || null,
      mini_package_title: body.miniPackageTitle.trim(),
      package_type: body.packageType,
      package_price: packagePrice,
      full_name: body.fullName.trim(),
      email: body.email.toLowerCase().trim(),
      phone: body.phone.trim(),
      number_of_travelers: numberOfTravelers,
      preferred_travel_date: body.preferredTravelDate,
      special_requests: body.specialRequests?.trim() || ''
    };

    // Insert booking into database
    const { data: newBooking, error } = await supabase
      .from('sas_mini_package_bookings')
      .insert([bookingData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create mini package booking in database' },
        { status: 500 }
      );
    }

    // Send confirmation email to customer (async, don't wait)
    sendMiniPackageBookingConfirmationEmail(newBooking).catch(error => {
      console.error('Failed to send confirmation email:', error);
    });

    // Send notification email to admin (async, don't wait)
    sendMiniPackageBookingNotificationEmail(newBooking).catch(error => {
      console.error('Failed to send admin notification email:', error);
    });

    return NextResponse.json({
      success: true,
      data: newBooking,
      message: 'Mini package booking created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating mini package booking:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH - Update mini package booking status (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, bookingStatus, paymentStatus, adminNotes } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Booking ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    
    if (bookingStatus) {
      const validBookingStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
      if (!validBookingStatuses.includes(bookingStatus)) {
        return NextResponse.json(
          { success: false, error: 'Invalid booking status' },
          { status: 400 }
        );
      }
      updateData.booking_status = bookingStatus;
    }

    if (paymentStatus) {
      const validPaymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
      if (!validPaymentStatuses.includes(paymentStatus)) {
        return NextResponse.json(
          { success: false, error: 'Invalid payment status' },
          { status: 400 }
        );
      }
      updateData.payment_status = paymentStatus;
    }

    if (adminNotes !== undefined) {
      updateData.admin_notes = adminNotes;
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update booking
    const { data, error } = await supabase
      .from('sas_mini_package_bookings')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update mini package booking' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { success: false, error: 'Mini package booking not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Mini package booking updated successfully'
    });

  } catch (error) {
    console.error('Error updating mini package booking:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
