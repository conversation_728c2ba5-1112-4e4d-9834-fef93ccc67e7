#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixMiniPackageData() {
  console.log('🔧 Fixing mini-package data issues...\n');

  try {
    // 1. Check current data for "the-last-login"
    console.log('📋 1. Checking current data for "the-last-login"...');

    const { data: currentData, error: fetchError } = await supabase
      .from('sas_mini_packages')
      .select('id, title, slug, highlights, packing_list, includes, excludes, status')
      .eq('slug', 'the-last-login')
      .single();

    if (fetchError) {
      console.error('❌ Error fetching mini-package:', fetchError.message);
      return;
    }

    if (!currentData) {
      console.log('❌ Mini-package "the-last-login" not found');
      return;
    }

    console.log(`✅ Found mini-package: "${currentData.title}"`);
    console.log(`   Status: ${currentData.status}`);
    console.log(`   Current highlights:`, currentData.highlights);
    console.log(`   Current packing_list:`, currentData.packing_list);
    console.log(`   Current includes:`, currentData.includes);
    console.log(`   Current excludes:`, currentData.excludes);

    // 2. Prepare correct data
    console.log('\n📝 2. Preparing correct data...');
    
    const correctHighlights = [
      "Immersive digital adventure experience",
      "Explore mysterious virtual landscapes",
      "Solve challenging puzzles and riddles",
      "Uncover hidden secrets and storylines",
      "Interactive gameplay with stunning visuals"
    ];

    const correctPackingList = [
      "Comfortable clothing for extended sessions",
      "Notebook and pen for taking notes",
      "Snacks and water for energy",
      "Headphones for better audio experience",
      "Backup power source for devices"
    ];

    const correctIncludes = [
      "Professional digital guide and support",
      "Access to all game levels and content",
      "Interactive tutorials and hints",
      "Achievement tracking and progress saving",
      "24/7 technical support during gameplay"
    ];

    const correctExcludes = [
      "Personal gaming devices (bring your own)",
      "Internet connection costs",
      "Additional downloadable content (DLC)",
      "Premium upgrade features",
      "Personal snacks and beverages"
    ];

    console.log('   New highlights:', correctHighlights);
    console.log('   New packing list:', correctPackingList);
    console.log('   New includes:', correctIncludes);
    console.log('   New excludes:', correctExcludes);

    // 3. Update the data
    console.log('\n🔄 3. Updating mini-package data...');
    
    const { data: updatedData, error: updateError } = await supabase
      .from('sas_mini_packages')
      .update({
        highlights: correctHighlights,
        packing_list: correctPackingList,
        includes: correctIncludes,
        excludes: correctExcludes,
        updated_at: new Date().toISOString()
      })
      .eq('slug', 'the-last-login')
      .select('id, title, highlights, packing_list, includes, excludes')
      .single();

    if (updateError) {
      console.error('❌ Error updating mini-package:', updateError.message);
      return;
    }

    console.log('✅ Successfully updated mini-package data!');
    console.log(`   Updated highlights:`, updatedData.highlights);
    console.log(`   Updated packing_list:`, updatedData.packing_list);
    console.log(`   Updated includes:`, updatedData.includes);
    console.log(`   Updated excludes:`, updatedData.excludes);

    // 4. Verify the fix by checking other mini-packages for similar issues
    console.log('\n🔍 4. Checking other mini-packages for similar data issues...');
    
    const { data: allMiniPackages, error: allError } = await supabase
      .from('sas_mini_packages')
      .select('id, title, slug, highlights, packing_list, includes, excludes')
      .eq('status', 'published');

    if (allError) {
      console.error('❌ Error fetching all mini-packages:', allError.message);
      return;
    }

    let issuesFound = 0;
    const problematicPackages = [];

    allMiniPackages.forEach(pkg => {
      let hasIssues = false;
      
      // Check for "Chapter" text in highlights
      if (pkg.highlights && pkg.highlights.some(h => h.includes('Chapter'))) {
        hasIssues = true;
      }
      
      // Check for "Chapter" text in packing_list
      if (pkg.packing_list && pkg.packing_list.some(p => p.includes('Chapter'))) {
        hasIssues = true;
      }

      // Check for "Chapter" text in includes
      if (pkg.includes && pkg.includes.some(i => i.includes('Chapter'))) {
        hasIssues = true;
      }

      // Check for "Chapter" text in excludes
      if (pkg.excludes && pkg.excludes.some(e => e.includes('Chapter'))) {
        hasIssues = true;
      }

      if (hasIssues) {
        issuesFound++;
        problematicPackages.push({
          title: pkg.title,
          slug: pkg.slug,
          highlights: pkg.highlights,
          packing_list: pkg.packing_list,
          includes: pkg.includes,
          excludes: pkg.excludes
        });
      }
    });

    if (issuesFound > 0) {
      console.log(`⚠️  Found ${issuesFound} mini-packages with similar data issues:`);
      problematicPackages.forEach(pkg => {
        console.log(`   - "${pkg.title}" (${pkg.slug})`);
        if (pkg.highlights && pkg.highlights.some(h => h.includes('Chapter'))) {
          console.log(`     ❌ Highlights contain "Chapter" text`);
        }
        if (pkg.packing_list && pkg.packing_list.some(p => p.includes('Chapter'))) {
          console.log(`     ❌ Packing list contains "Chapter" text`);
        }
        if (pkg.includes && pkg.includes.some(i => i.includes('Chapter'))) {
          console.log(`     ❌ Includes contains "Chapter" text`);
        }
        if (pkg.excludes && pkg.excludes.some(e => e.includes('Chapter'))) {
          console.log(`     ❌ Excludes contains "Chapter" text`);
        }
      });
      
      console.log('\n💡 You may want to fix these manually in the admin panel.');
    } else {
      console.log('✅ No other mini-packages found with similar issues.');
    }

    console.log('\n🎉 Fix completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Refresh the mini-package page to see the changes');
    console.log('   2. Check the "What to Pack" section displays correctly');
    console.log('   3. Verify the highlights section shows proper content');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixMiniPackageData();
