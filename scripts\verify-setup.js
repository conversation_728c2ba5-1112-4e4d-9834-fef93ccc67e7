#!/usr/bin/env node

/**
 * Setup Verification Script
 * Checks if Tailwind CSS and project dependencies are properly configured
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 Verifying Swift Africa Safaris setup...\n');

// Check for required files
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'tailwind.config.js'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(process.cwd(), file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// Check for environment variables
const envPath = path.join(process.cwd(), '.env.local');
const envExists = fs.existsSync(envPath);

if (envExists) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const hasSupabaseUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL');
  const hasSupabaseKey = envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY');
  const hasTinyMCEKey = envContent.includes('TINYMCE_API_KEY');

  console.log(`\n${hasSupabaseUrl ? '✅' : '❌'} NEXT_PUBLIC_SUPABASE_URL configured`);
  console.log(`${hasSupabaseKey ? '✅' : '❌'} NEXT_PUBLIC_SUPABASE_ANON_KEY configured`);
  console.log(`${hasTinyMCEKey ? '✅' : '❌'} TINYMCE_API_KEY configured`);

  if (!hasSupabaseUrl || !hasSupabaseKey) {
    console.log('\n⚠️  Supabase environment variables not configured');
    console.log('Run: npm run setup-supabase');
  }

  if (!hasTinyMCEKey) {
    console.log('\n⚠️  TinyMCE API key not configured');
    console.log('Add TINYMCE_API_KEY to your .env.local file');
  }
} else {
  console.log('\n❌ .env.local not found');
  console.log('Run: npm run setup-supabase');
}

// Check for Supabase client
const supabasePath = path.join(process.cwd(), 'lib', 'supabase.ts');
const supabaseExists = fs.existsSync(supabasePath);
console.log(`\n${supabaseExists ? '✅' : '❌'} Supabase client configured`);

// Check for admin API
const adminApiPath = path.join(process.cwd(), 'lib', 'admin-api.ts');
const adminApiExists = fs.existsSync(adminApiPath);
console.log(`${adminApiExists ? '✅' : '❌'} Admin API configured`);

console.log('\n📋 Setup Summary:');
console.log(`- Project files: ${allFilesExist ? '✅' : '❌'}`);
console.log(`- Environment: ${envExists ? '✅' : '❌'}`);
console.log(`- Supabase client: ${supabaseExists ? '✅' : '❌'}`);
console.log(`- Admin API: ${adminApiExists ? '✅' : '❌'}`);
console.log(`- TinyMCE API: ${envExists && fs.readFileSync(envPath, 'utf8').includes('TINYMCE_API_KEY') ? '✅' : '❌'}`);

if (allFilesExist && envExists && supabaseExists && adminApiExists) {
  console.log('\n🎉 Setup looks good! You can now run: npm run dev');
} else {
  console.log('\n⚠️  Some components are missing. Please complete the setup.');
}
