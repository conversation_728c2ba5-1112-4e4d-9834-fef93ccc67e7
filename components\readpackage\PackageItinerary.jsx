"use client";

import React, { useState } from 'react';
import { FaPlus, FaMinus } from 'react-icons/fa';

const PackageItinerary = ({ itinerary = [] }) => {
  const [expandedDay, setExpandedDay] = useState(1);

  // Handle empty itinerary
  if (!itinerary || itinerary.length === 0) {
    return (
      <section className="mb-12 text-justify">
        <h2 className="text-3xl font-bold mb-6 text-[var(--primary-color)]">Itineraries</h2>
        <p className="text-gray-600">No itinerary available for this package.</p>
      </section>
    );
  }

  return (
    <section className="mb-12 text-justify">
      <h2 className="text-3xl font-bold mb-6 text-[var(--primary-color)]">Itineraries</h2>

      {itinerary.map((item, index) => {
        // Handle both database structure (day_number) and legacy structure (day)
        const dayNumber = item.day_number || item.day || index + 1;
        const itemId = item.id || `day-${dayNumber}`;

        return (
          <div key={itemId} className="mb-4 bg-[var(--btn)]">
            <div
              className={`cursor-pointer  ${expandedDay === dayNumber ? 'bg-[var(--accent)]' : 'bg-[var(--btn)]'}
                text-gray-50 font-medium p-4 flex justify-between items-center`}
              onClick={() => setExpandedDay(expandedDay === dayNumber ? null : dayNumber)}
            >
              <h3 className="text-lg ">Day {dayNumber}: {item.title}</h3>
              <span>{expandedDay === dayNumber ? <FaMinus /> : <FaPlus />}</span>
            </div>

            {expandedDay === dayNumber && (
              <div className="p-4 border border-t-0 border-gray-200 bg-[var(--secondary-background)]">
                <p className="text-gray-700">{item.description}</p>

                {/* Display activities if available */}
                {item.activities && item.activities.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Activities:</h4>
                    <ul className="list-disc list-inside text-gray-700">
                      {item.activities.map((activity, actIndex) => (
                        <li key={`${itemId}-activity-${actIndex}`}>{activity}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Display accommodation if available */}
                {item.accommodation && (
                  <div className="mt-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Accommodation:</h4>
                    <p className="text-gray-700">{item.accommodation}</p>
                  </div>
                )}

                {/* Display meals if available */}
                {item.meals && item.meals.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Meals:</h4>
                    <ul className="list-disc list-inside text-gray-700">
                      {item.meals.map((meal, mealIndex) => (
                        <li key={`${itemId}-meal-${mealIndex}`}>{meal}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </section>
  );
};

export default PackageItinerary;
