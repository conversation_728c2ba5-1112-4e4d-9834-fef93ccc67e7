import { notFound } from 'next/navigation'
import type { Metadata } from 'next'
import MegaMenuHeader from '@/components/header'
import TourismFooter from '@/components/footer'
import StructuredData from '@/components/common/StructuredData'
import BlogHero from '@/components/blog-reader/BlogHero'
import ContentRenderer from '@/components/blog-reader/ContentRenderer'
import RelatedPosts from '@/components/blog-reader/RelatedPosts'
import BlogComments from '@/components/blog-reader/BlogComments'
import BlogSidebar from '@/components/blog-reader/BlogSidebar'
import { 
  fetchBlogPostBySlug, 
  fetchRelatedPosts, 
  generateBlogStaticParams,
  incrementViewCount 
} from '@/lib/blog-service'
import { generateMetadata as generateSEOMetadata, generateArticleSchema, generateBreadcrumbSchema } from '@/lib/seo'

interface PageProps {
  params: Promise<{ slug: string }>
}

// Generate static params for all published blog posts
export async function generateStaticParams() {
  try {
    console.log('🔄 Generating static params for blog posts...')
    const params = await generateBlogStaticParams()
    console.log(`✅ Generated ${params.length} blog post static params`)
    return params
  } catch (error) {
    console.error('❌ Error generating static params:', error)
    return []
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params
  
  try {
    const blogPost = await fetchBlogPostBySlug(slug)

    if (!blogPost) {
      return generateSEOMetadata({
        title: 'Blog Post Not Found',
        description: 'The requested blog post could not be found.',
        url: `/blog/${slug}`
      })
    }

    // Use database SEO metadata with fallbacks
    return generateSEOMetadata({
      title: blogPost.seo_title || `${blogPost.title} - Swift Africa Safaris Blog`,
      description: blogPost.seo_description || blogPost.description,
      keywords: blogPost.seo_keywords || [],
      type: 'article',
      url: `/blog/${slug}`,
      image: blogPost.og_image_url || blogPost.hero_image_url,
      publishedTime: blogPost.published_at,
      modifiedTime: blogPost.updated_at,
      section: blogPost.category,
      tags: blogPost.tags || []
    })
  } catch (error) {
    console.error('❌ Error generating metadata:', error)
    return generateSEOMetadata({
      title: 'Blog Post - Swift Africa Safaris',
      description: 'Discover amazing stories and insights about African safaris.',
      url: `/blog/${slug}`
    })
  }
}

// Main blog post page component
export default async function BlogPostPage({ params }: PageProps) {
  const { slug } = await params

  try {
    console.log(`🔍 Loading blog post: ${slug}`)

    // Fetch the main blog post
    const blogPost = await fetchBlogPostBySlug(slug)

    if (!blogPost) {
      console.log(`❌ Blog post not found: ${slug}`)
      notFound()
    }

    console.log(`✅ Blog post loaded: ${blogPost.title}`)

    // Fetch related posts in parallel
    const relatedPostsPromise = fetchRelatedPosts(blogPost.category, blogPost.id, 3)

    // Increment view count (fire and forget)
    incrementViewCount(blogPost.id).catch(error => 
      console.error('❌ Error incrementing view count:', error)
    )

    // Wait for related posts
    const relatedPosts = await relatedPostsPromise

    // Generate structured data
    const breadcrumbSchema = generateBreadcrumbSchema([
      { name: 'Home', url: '/' },
      { name: 'Blog', url: '/blog' },
      { name: blogPost.title, url: `/blog/${slug}` }
    ])

    const articleSchema = generateArticleSchema({
      title: blogPost.title,
      description: blogPost.description,
      image: blogPost.hero_image_url,
      publishedTime: blogPost.published_at,
      modifiedTime: blogPost.updated_at,
      author: 'Swift Africa Safaris',
      url: `/blog/${slug}`,
      tags: blogPost.tags || []
    })

    console.log(`✅ Page ready with ${blogPost.content_blocks?.length || 0} content blocks and ${relatedPosts.length} related posts`)

    return (
      <div className="bg-white min-h-screen">
        {/* Structured Data */}
        <StructuredData data={[breadcrumbSchema, articleSchema]} />
        
        {/* Header */}
        <MegaMenuHeader />
        
        {/* Main Content */}
        <main>
          {/* Hero Section */}
          <BlogHero
            title={blogPost.title}
            heroImage={blogPost.hero_image_url}
            heroImageAlt={blogPost.hero_image_alt}
            category={blogPost.category}
            publishedAt={blogPost.published_at}
            viewCount={blogPost.view_count}
            tags={blogPost.tags || []}
          />

          {/* Blog Content */}
          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Content */}
                  <div className="lg:col-span-2">
                    {/* Article Content */}
                    <article className="prose prose-lg max-w-none">
                      <ContentRenderer contentBlocks={blogPost.content_blocks || []} />
                    </article>

                    {/* Article Footer */}
                    <div className="mt-12 pt-8 border-t border-gray-200">
                      <div className="flex flex-wrap items-center justify-between gap-4">
                        {/* Tags */}
                        {blogPost.tags && blogPost.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            <span className="text-sm font-medium text-gray-600">Tags:</span>
                            {blogPost.tags.map((tag, index) => (
                              <span
                                key={index}
                                className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                              >
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Share Buttons */}
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-600">Share:</span>
                          <div className="flex gap-2">
                            <button className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                              </svg>
                            </button>
                            <button className="p-2 bg-blue-800 text-white rounded-lg hover:bg-blue-900 transition-colors">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                              </svg>
                            </button>
                            <button className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.085"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Sidebar */}
                  <div className="lg:col-span-1">
                    <div className="sticky top-8">
                      <BlogSidebar currentSlug={slug} currentCategory={blogPost.category} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <RelatedPosts posts={relatedPosts} currentCategory={blogPost.category} />
          )}

          {/* Comments Section */}
          <BlogComments blogSlug={slug} />
        </main>

        {/* Footer */}
        <TourismFooter />
      </div>
    )

  } catch (error) {
    console.error(`❌ Error loading blog post ${slug}:`, error)
    notFound()
  }
}

// Configure ISR
export const revalidate = 300 // 5 minutes
